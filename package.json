{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint .", "prepare": "husky install"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/pro-components": "^2.8.9", "@clerk/backend": "^2.0.0", "@icons-pack/react-simple-icons": "^13.0.0", "@lobehub/chat-plugin-sdk": "^1.32.4", "@lobehub/icons": "^2.2.0", "@lobehub/tts": "^2.0.1", "@lobehub/ui": "^2.5.6", "@neondatabase/serverless": "^1.0.0", "@t3-oss/env-nextjs": "^0.13.6", "@tanstack/react-query": "5.76.1", "@types/debug": "4.1.12", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/nodemailer": "^6.4.17", "@types/numeral": "2.0.5", "@types/semver": "^7.7.0", "@types/ua-parser-js": "0.7.39", "@types/unist": "3.0.3", "@vercel/speed-insights": "1.2.0", "ahooks": "^3.8.5", "antd": "^5.25.4", "antd-style": "^3.7.1", "axios": "^1.9.0", "dayjs": "^1.11.13", "debug": "4.4.1", "dexie": "^4.0.11", "diff": "^8.0.2", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.41.0", "drizzle-zod": "^0.8.2", "fast-deep-equal": "^3.1.3", "framer-motion": "^12.15.0", "gpt-tokenizer": "2.9.0", "idb-keyval": "^6.2.2", "immer": "^10.1.1", "jose": "^6.0.11", "js-cookie": "^3.0.5", "js-sha256": "^0.11.1", "jsonl-parse-stringify": "^1.0.3", "lodash-es": "^4.17.21", "lucide-react": "^0.511.0", "mdast-util-to-markdown": "^2.1.2", "next": "15.3.2", "next-auth": "5.0.0-beta.29", "nodemailer": "^7.0.3", "numeral": "^2.0.6", "nuqs": "^2.4.3", "oidc-client-ts": "^3.2.1", "oidc-provider": "8.8.1", "p-map": "7.0.3", "partial-json": "^0.1.7", "pg": "^8.16.3", "polished": "^4.3.1", "query-string": "^9.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-layout-kit": "^1.9.1", "react-lazy-load": "^4.0.1", "react-virtuoso": "^4.12.8", "react-wrap-balancer": "^1.1.1", "semver": "^7.7.2", "superjson": "^2.2.2", "swr": "^2.3.3", "tokenx": "0.4.1", "ts-md5": "^1.3.1", "ua-parser-js": "^1.0.40", "unist": "^0.0.1", "unist-util-visit": "^5.0.0", "url-join": "^5.0.0", "use-merge-value": "^1.2.0", "utility-types": "^3.11.0", "uuid": "^11.1.0", "yaml": "^2.8.0", "zod": "^3.25.48", "zustand": "^5.0.5", "zustand-utils": "^2.1.0"}, "devDependencies": {"@eslint/js": "^9.32.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/pg": "^8.15.4", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.29.0", "eslint-config-next": "15.3.4", "husky": "^8.0.0", "tailwindcss": "^4", "typescript": "^5"}}